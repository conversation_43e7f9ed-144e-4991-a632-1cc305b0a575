"""
smart audio volume processing module for intelligent audio analysis and dynamic adjustments.

this module provides advanced audio processing capabilities including:
- dynamic vocal boost based on content analysis
- lufs-based loudness normalization
- background ducking during speech segments
- peak limiting and quality validation
- cross-chunk consistency analysis

all functions include fallback mechanisms to ensure pipeline stability.
"""

import logging
import numpy as np
from typing import Optional, Tuple, Dict, Any
from pathlib import Path

try:
    import librosa
    import pyloudnorm as pyln
    from scipy import signal

    SMART_AUDIO_AVAILABLE = True
except ImportError as e:
    logging.warning(f"smart audio dependencies not available: {e}")
    SMART_AUDIO_AVAILABLE = False

from pydub import AudioSegment
from ..config import AudioProcessingConfig
from ..utils.logger import logger


class SmartAudioProcessor:
    """intelligent audio processor with fallback mechanisms."""

    def __init__(self, config: AudioProcessingConfig):
        self.config = config
        self.meter = None

        if SMART_AUDIO_AVAILABLE and config.smart_volume:
            try:
                # initialize loudness meter for lufs analysis
                self.meter = pyln.Meter(48000)  # use maximum sample rate
                logger().info("smart audio processing initialized successfully")
            except Exception as e:
                logger().warning(f"failed to initialize smart audio processing: {e}")
                self.meter = None
        else:
            logger().info("using static audio processing (smart processing disabled)")

    def analyze_audio_loudness(self, audio_file: str) -> Dict[str, float]:
        """
        analyze audio file for loudness characteristics.

        returns:
            dict with loudness metrics or empty dict if analysis fails
        """
        if not self.meter or not SMART_AUDIO_AVAILABLE:
            return {}

        try:
            # load audio with librosa
            audio_data, sample_rate = librosa.load(audio_file, sr=48000, mono=True)

            # calculate lufs loudness
            loudness_lufs = self.meter.integrated_loudness(audio_data)

            # calculate additional metrics
            rms_level = np.sqrt(np.mean(audio_data**2))
            peak_level = np.max(np.abs(audio_data))
            dynamic_range = peak_level - rms_level if peak_level > 0 else 0

            # calculate spectral centroid for content analysis
            spectral_centroid = np.mean(
                librosa.feature.spectral_centroid(y=audio_data, sr=sample_rate)
            )

            return {
                "loudness_lufs": loudness_lufs,
                "rms_level": float(rms_level),
                "peak_level": float(peak_level),
                "dynamic_range": float(dynamic_range),
                "spectral_centroid": float(spectral_centroid),
                "duration": len(audio_data) / sample_rate,
            }

        except Exception as e:
            logger().warning(f"audio loudness analysis failed: {e}")
            return {}

    def calculate_dynamic_vocal_boost(
        self, vocal_metrics: Dict[str, float], background_metrics: Dict[str, float]
    ) -> float:
        """
        calculate optimal vocal boost based on content analysis.

        returns:
            boost amount in db (0.0 to max_boost_db)
        """
        if not vocal_metrics or not self.config.dynamic_vocal_boost.enabled:
            return 5.0  # fallback to static boost

        try:
            vocal_lufs = vocal_metrics.get("loudness_lufs", -30.0)
            background_lufs = background_metrics.get("loudness_lufs", -40.0)

            # calculate boost based on loudness difference
            loudness_diff = background_lufs - vocal_lufs

            # base boost calculation
            if loudness_diff > 10:
                # background much louder than vocals
                boost = self.config.dynamic_vocal_boost.max_boost_db
            elif loudness_diff > 5:
                # moderate difference
                boost = self.config.dynamic_vocal_boost.max_boost_db * 0.7
            elif loudness_diff > 0:
                # slight difference
                boost = self.config.dynamic_vocal_boost.max_boost_db * 0.4
            else:
                # vocals already loud enough
                boost = self.config.dynamic_vocal_boost.min_boost_db

            # adjust based on spectral content
            vocal_brightness = vocal_metrics.get("spectral_centroid", 2000)
            if vocal_brightness < 1500:  # darker voice, needs more boost
                boost *= 1.2
            elif vocal_brightness > 3000:  # brighter voice, needs less boost
                boost *= 0.8

            # clamp to configured range
            boost = max(
                self.config.dynamic_vocal_boost.min_boost_db,
                min(self.config.dynamic_vocal_boost.max_boost_db, boost),
            )

            logger().debug(f"calculated dynamic vocal boost: {boost:.1f}db")
            return boost

        except Exception as e:
            logger().warning(f"dynamic vocal boost calculation failed: {e}")
            return 5.0  # fallback to static boost

    def apply_background_ducking(
        self, background_audio: AudioSegment, vocal_segments: list
    ) -> AudioSegment:
        """
        apply automatic background ducking during vocal segments.

        returns:
            processed background audio with ducking applied
        """
        if not self.config.background_ducking.enabled:
            return background_audio

        try:
            ducked_audio = background_audio
            reduction_db = self.config.background_ducking.reduction_db

            for segment in vocal_segments:
                start_ms = int(segment.get("start", 0) * 1000)
                end_ms = int(segment.get("end", 0) * 1000)

                if start_ms < len(ducked_audio) and end_ms > 0:
                    # apply ducking to this segment
                    segment_audio = ducked_audio[start_ms:end_ms]
                    ducked_segment = segment_audio + reduction_db

                    # replace the segment
                    ducked_audio = (
                        ducked_audio[:start_ms] + ducked_segment + ducked_audio[end_ms:]
                    )

            logger().debug(f"applied background ducking: {reduction_db}db reduction")
            return ducked_audio

        except Exception as e:
            logger().warning(f"background ducking failed: {e}")
            return background_audio

    def apply_peak_limiting(self, audio: AudioSegment) -> AudioSegment:
        """
        apply intelligent peak limiting to prevent clipping.

        returns:
            processed audio with peak limiting applied
        """
        if not self.config.peak_limiting.enabled:
            return audio

        try:
            # simple peak limiting using pydub
            threshold_db = self.config.peak_limiting.threshold_db

            # check if limiting is needed
            if audio.max_dBFS <= threshold_db:
                return audio  # no limiting needed

            # apply limiting
            limited_audio = audio.normalize(headroom=abs(threshold_db))

            logger().debug(f"applied peak limiting at {threshold_db}db")
            return limited_audio

        except Exception as e:
            logger().warning(f"peak limiting failed: {e}")
            return audio

    def validate_audio_quality(self, audio_file: str) -> bool:
        """
        validate audio quality and detect potential artifacts.

        returns:
            true if audio quality is acceptable
        """
        if not self.config.quality_validation.enabled or not SMART_AUDIO_AVAILABLE:
            return True  # assume ok if validation disabled

        try:
            audio_data, sample_rate = librosa.load(audio_file, sr=None, mono=True)

            # check for clipping
            clipping_ratio = np.sum(np.abs(audio_data) >= 0.99) / len(audio_data)
            if clipping_ratio > 0.01:  # more than 1% clipping
                logger().warning(
                    f"audio quality issue: {clipping_ratio*100:.1f}% clipping detected"
                )
                return False

            # check signal-to-noise ratio (simplified)
            signal_power = np.mean(audio_data**2)
            if signal_power < 1e-6:  # very low signal
                logger().warning("audio quality issue: very low signal level")
                return False

            logger().debug("audio quality validation passed")
            return True

        except Exception as e:
            logger().warning(f"audio quality validation failed: {e}")
            return True  # assume ok if validation fails


def create_smart_processor(config: AudioProcessingConfig) -> SmartAudioProcessor:
    """create a smart audio processor instance with the given configuration."""
    return SmartAudioProcessor(config)


def fallback_to_static_processing(
    vocals_volume_adjustment: float = 5.0, background_volume_adjustment: float = 0.0
) -> Tuple[float, float]:
    """
    fallback function that returns static volume adjustments.

    returns:
        tuple of (vocal_boost_db, background_adjustment_db)
    """
    logger().info("using static audio processing (fallback)")
    return vocals_volume_adjustment, background_volume_adjustment


def safe_smart_processing(
    audio_config,
    background_file: str,
    vocals_file: str,
    vocal_segments: Optional[list] = None,
) -> Dict[str, Any]:
    """
    safely attempt smart audio processing with comprehensive error handling.

    returns:
        dict with processing results or fallback values
    """
    result = {
        "success": False,
        "vocal_boost": 5.0,
        "background_processed": None,
        "error": None,
    }

    if not audio_config or not audio_config.smart_volume:
        result["error"] = "smart processing disabled"
        return result

    try:
        processor = create_smart_processor(audio_config)

        # analyze audio files
        background_metrics = processor.analyze_audio_loudness(background_file)
        vocal_metrics = processor.analyze_audio_loudness(vocals_file)

        # calculate dynamic boost
        if background_metrics and vocal_metrics:
            result["vocal_boost"] = processor.calculate_dynamic_vocal_boost(
                vocal_metrics, background_metrics
            )

        # apply background ducking if segments provided
        if vocal_segments and audio_config.background_ducking.enabled:
            from pydub import AudioSegment

            background_audio = AudioSegment.from_file(background_file)
            result["background_processed"] = processor.apply_background_ducking(
                background_audio, vocal_segments
            )

        result["success"] = True
        logger().info(
            f"smart audio processing successful: boost={result['vocal_boost']:.1f}db"
        )

    except Exception as e:
        result["error"] = str(e)
        logger().warning(f"smart audio processing failed: {e}")

    return result
